using System.Diagnostics;
using AvrStream.Gateway.Api.Models.Responses;
using AvrStream.Gateway.Api.Services;
using Microsoft.AspNetCore.Mvc;

namespace AvrStream.Gateway.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BoxController : ControllerBase
    {
        private readonly RecordService _recordService;
        private readonly ILogger<BoxController> _logger;

        public BoxController(RecordService recordService, ILogger<BoxController> logger)
        {
            _recordService = recordService;
            _logger = logger;
        }

        [HttpGet("start/{cameraId}")]
        public async Task<IActionResult> Start(string cameraId)
        {
            var res = await _recordService.Start(cameraId);
            if (!res)
            {
                return Ok(new BaseResponse
                {
                    Success = true,
                    Message = $"Start recording device {cameraId} failed"
                });
            }

            return Ok(new BaseResponse
            {
                Success = true,
                Message = $"Started recording device {cameraId} successfully"
            });
        }

        [HttpGet("startall")]
        public async Task<IActionResult> StartAll()
        {
            await _recordService.StartAll();
            return Ok($"Started recording for all camera.");
        }

        [HttpGet("stop/{cameraId}")]
        public IActionResult Stop(string cameraId)
        {
            var res = _recordService.Stop(cameraId);
            if (!res)
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Stoped recording device {cameraId} failed"
                });
            }

            return Ok(new BaseResponse
            {
                Success = true,
                Message = $"Stoped recording device {cameraId} successfully"
            });
        }

        [HttpGet("restart/{cameraId}")]
        public async Task<IActionResult> Restart(string cameraId)
        {
            var res = await _recordService.Restart(cameraId);
            if (!res)
            {
                return Ok(new BaseResponse
                {
                    Success = false,
                    Message = $"Failed to restart recording for camera {cameraId}."
                });
            }

            return Ok(new BaseResponse
            {
                Success = true,
                Message = $"Restarted recording for camera {cameraId}."
            });
        }

        [HttpGet("reset-box")]
        public IActionResult ResetBox()
        {
            _recordService.StopAll();
            var res = RestartSystem();

            return Ok(new BaseResponse
            {
                Success = res,
                Message = res ? "Box is restarting, please wait" : "Box reboot failed"
            });
        }

        [HttpGet("force-reset-box")]
        public IActionResult ForceResetBox()
        {
            Task.Run(RestartSystem);
            return Ok(new BaseResponse
            {
                Success = true,
                Message = "Box is restarting, please wait"
            });
        }

        private bool RestartSystem()
        {
            try
            {
                var info = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = "-c \"reboot\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(info);

                if (process == null)
                {
                    _logger.LogInformation("Failed to start the reboot process.");
                    return false;
                }

                // Wait for the process to exit (with a timeout for safety)
                if (!process.WaitForExit(10000)) // 10 seconds timeout
                {
                    process.Kill(); // Force kill if it takes too long
                    _logger.LogInformation("Reboot command timed out and was killed.");
                    return false;
                }

                // Check exit code (0 usually indicates success)
                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Reboot command executed successfully.");
                    return true;
                }

                // Log any error output
                var error = process.StandardError.ReadToEnd();
                if (!string.IsNullOrEmpty(error))
                {
                    _logger.LogInformation("Error during reboot: " + error);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("An error occurred while attempting to reboot: " + ex.Message);
                return false;
            }
        }
    }
}