using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using AvrStream.Gateway.Api.Models.Requests;
using AvrStream.Gateway.Api.Utils;
using AvrStream.Gateway.Entities.Enums;
using AvrStream.Gateway.Entities.Models;
using AvrStream.Gateway.Streamer;
using AvrStream.Gateway.Streamer.Pipes;

namespace AvrStream.Gateway.Api.Services;

public class RecordService
{
    public SystemSetting SysSetting;
    private readonly IConfiguration _configuration;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IServiceScopeFactory _factory;
    private readonly ILogger<RecordService> _logger;
    private readonly MessageService _messageService;
    private readonly RecordingFileService _recordingFileService;
    private readonly DeviceService _deviceService;
    private readonly CameraSettingService _cameraSettingService;
    public static bool IsStarting1;
    public static bool IsStarting2;
    public List<RecordPipeManager> RecordPipeManagers { get; set; } = new();

    public RecordService(IConfiguration configuration, ILoggerFactory loggerFactory, IServiceScopeFactory factory,
        ILogger<RecordService> logger,
        MessageService messageService, RecordingFileService recordingFileService, DeviceService deviceService,
        CameraSettingService cameraSettingService)
    {
        _configuration = configuration;
        _loggerFactory = loggerFactory;
        _factory = factory;
        _logger = logger;
        _messageService = messageService;
        _recordingFileService = recordingFileService;
        _deviceService = deviceService;
        _cameraSettingService = cameraSettingService;
    }

    public bool Stop(string pipelineId)
    {
        try
        {
            var recordPipeManager = RecordPipeManagers.FirstOrDefault(x => x.PipeName == pipelineId);
            if (recordPipeManager == null)
            {
                _messageService.CreateMessage($"Device {pipelineId} not found to stop", MessageType.Error);
                return false;
            }

            if (!recordPipeManager.IsRunning)
            {
                _messageService.CreateMessage($"Recording device {pipelineId} is not running",
                    MessageType.Alert);
                return false;
            }

            recordPipeManager.RecordPipe.Stop();
            recordPipeManager.IsRunning = false;
            _messageService.CreateMessage(
                $"Device {pipelineId} stopped successfully",
                MessageType.Alert);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return false;
        }
    }

    public void StopAll()
    {
        var recordPipeManagers = RecordPipeManagers.FindAll(t => t.IsRunning);
        foreach (var recordPipeManager in recordPipeManagers)
        {
            try
            {
                recordPipeManager.RecordPipe.Stop();
                recordPipeManager.IsRunning = false;
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
            }
        }
    }

    public async Task<bool> Restart(CameraRecordSetting cameraRecordSetting)
    {
        var recordPipeManager = RecordPipeManagers.FirstOrDefault(x =>
            (x.RecordSetting.CameraRecordSetting.Name == cameraRecordSetting.Name) &&
            (x.RecordSetting.CameraRecordSetting.Bus == cameraRecordSetting.Bus));
        if (recordPipeManager is not { IsRunning: true })
        {
            _messageService.CreateMessage($"Device {cameraRecordSetting.Name} not found to restart",
                MessageType.Error);
            return Task.FromResult(false).Result;
        }

        var videoCap = recordPipeManager.RecordSetting.Cam.Caps.FirstOrDefault(t =>
            t.Fps == cameraRecordSetting.Fps &&
            t.Width == Utilities.ConvertResolution(cameraRecordSetting.Resolution));
        if (videoCap == null)
        {
            _messageService.CreateMessage($"Suitable configuration not found for {cameraRecordSetting.Name}",
                MessageType.Error);
            return false;
        }

        recordPipeManager.RecordSetting.CameraRecordSetting = cameraRecordSetting;
        recordPipeManager.RecordSetting.Cap = videoCap;
        return await Restart(recordPipeManager.PipeName);
    }

    public async Task<bool> Restart(string pipelineId)
    {
        Stop(pipelineId);
        await Task.Delay(500);
        var res = await Start(pipelineId);
        return res;
    }


    public async Task StartAll()
    {
        _logger.LogInformation("RecordPipe available: " + RecordPipeManagers.Count);
        foreach (var recordPipeManager in RecordPipeManagers)
        {
            try
            {
                await Start(recordPipeManager.PipeName);
            }
            catch (Exception e)
            {
                _logger.LogError(e, null);
            }
        }
    }

    public async Task RestartAll(SystemSetting systemSetting)
    {
        SysSetting = systemSetting;
        await Init(SysSetting);
        foreach (var recordPipeManager in RecordPipeManagers)
        {
            var res = await Restart(recordPipeManager.PipeName);
            if (!res)
            {
                _messageService.CreateMessage($"Cannot restart {recordPipeManager.PipeName}",
                    MessageType.Error);
            }
        }
    }

    public async Task<bool> Start(string pipelineId)
    {
        await Init(SysSetting);
        var recordPipeManager = RecordPipeManagers.FirstOrDefault(x => x.PipeName == pipelineId);
        if (recordPipeManager == null)
        {
            _messageService.CreateMessage($"Device {pipelineId} not found to start", MessageType.Error);
            return false;
        }

        if (recordPipeManager.IsRunning)
        {
            _messageService.CreateMessage($"Recording device {pipelineId} is running", MessageType.Alert);
            return false;
        }

        var res = true;
        if (pipelineId == "cam1")
        {
            IsStarting1 = true;
        }
        else if (pipelineId == "cam2")
        {
            IsStarting2 = true;
        }

        var recordPipe = new RecordPipe(_loggerFactory,
            SysSetting,
            recordPipeManager.RecordSetting,
            (_, changing) =>
            {
                try
                {
                    _logger.LogInformation($"Changing: {changing}");
                    if (!changing)
                    {
                        _messageService.CreateMessage($"Started recording device {pipelineId}", MessageType.Info);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            },
            (addedFile) =>
            {
                try
                {
                    _logger.LogInformation($"Added file: {addedFile}");
                    var result = _recordingFileService.CreateOrUpdateRecordingFile(
                        new CreateOrUpdateRecordingFileRequest
                        {
                            Name = Path.GetFileName(addedFile),
                            Path = addedFile,
                            IsEndRecordingFile = true,
                            IsSynchronized = false,
                            EncryptDone = false,
                            IsEncrypt = SysSetting.IsEncrypted,
                            Password = SysSetting.Password,
                            Iv = SysSetting.Iv
                        });
                    if (result.Success)
                    {
                        _messageService.CreateMessage($"File {Path.GetFileName(addedFile)} has been added",
                            MessageType.Info);
                    }
                    else
                    {
                        _messageService.CreateMessage($"Adding file {Path.GetFileName(addedFile)} error: {result.Message}",
                            MessageType.Error);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            },
            () =>
            {
                res = false;
                try
                {
                    _logger.LogInformation($"Stopped recording {pipelineId}");
                    _messageService.CreateMessage($"Stopped recording device {pipelineId}", MessageType.Info);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            },
            (err) =>
            {
                try
                {
                    res = false;
                    _logger.LogError($"Recording error {pipelineId}: {err}");
                    var file = Utilities.GetValidFile(recordPipeManager.RecordSetting);
                    _logger.LogInformation($"Trying to add the latest file {file}");
                    if (file != null)
                    {
                        // add the latest file to a record
                        var result = _recordingFileService.CreateOrUpdateRecordingFile(
                            new CreateOrUpdateRecordingFileRequest
                            {
                                Name = Path.GetFileName(file),
                                Path = file,
                                IsEndRecordingFile = true,
                                IsSynchronized = false,
                                EncryptDone = false,
                                IsEncrypt = SysSetting.IsEncrypted,
                                Password = SysSetting.Password,
                                Iv = SysSetting.Iv
                            });
                        if (result.Success)
                        {
                            _messageService.CreateMessage($"File {Path.GetFileName(file)} has been added",
                                MessageType.Info);
                        }
                        else
                        {
                            _messageService.CreateMessage($"Adding file {Path.GetFileName(file)} error: {result.Message}",
                                MessageType.Error);
                        }

                        _messageService.CreateMessage($"Recording device {pipelineId} error: {err}", MessageType.Error);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            });
        recordPipeManager.RecordPipe = recordPipe;
        recordPipeManager.IsRunning = true;
        if (pipelineId == "cam1")
        {
            IsStarting1 = false;
        }
        else if (pipelineId == "cam2")
        {
            IsStarting2 = false;
        }

        return res;
    }

    public async Task<bool> Init(SystemSetting systemSetting)
    {
        using var scope = _factory.CreateScope();
        SysSetting = systemSetting;
        var result = _deviceService.ListAvrDevicesAsync();
        if (result == null)
        {
            _messageService.CreateMessage("Device not found", MessageType.Error);
            return Task.FromResult(false).Result;
        }

        _logger.LogInformation("DeviceService available: " + result.Count);

        var online = Utilities.PingHost(SysSetting.ServerUrl);
        var file = _configuration.GetValue<string>("UniqueFilePath");
        var uniqueId = await File.ReadAllTextAsync(file);
        var pattern = @"[^a-zA-Z0-9_-]";
        uniqueId = Regex.Replace(uniqueId, pattern, "");
        if (_deviceService.Cam1 != null && _deviceService.Mic1 != null)
        {
            var videoCap = _deviceService.Cam1.Caps.FirstOrDefault();
            var cameraRecordSetting =
                _cameraSettingService.GetActiveCameraRecordSetting(_deviceService.Cam1.DisplayName,
                    _deviceService.Cam1.Bus);
            if (cameraRecordSetting == null)
            {
                // create new camera record setting
                var supportFps = _configuration.GetSection("RecordSetting:SupportedFps").Get<List<int>>();
                foreach (var fps in supportFps)
                {
                    videoCap = _deviceService.Cam1.Caps.FirstOrDefault(x => x.Fps == fps);
                    if (videoCap != null)
                    {
                        break;
                    }
                }

                var res = await _cameraSettingService.CreateOrUpdateCameraRecordSettingAsync(
                    new CreateOrUpdateCameraRecordSettingRequest
                    {
                        Name = _deviceService.Cam1.DisplayName,
                        Bus = _deviceService.Cam1.Bus,
                        IsActive = true,
                        // get resolution if  width x height = 1920 x 1080 => FullHd else if 1280 x 720 => Hd else if 3840 x 2160 => UltraHd
                        Resolution = videoCap!.Width switch
                        {
                            1280 => CameraResolution.Hd,
                            1920 => CameraResolution.FullHd,
                            3840 => CameraResolution.UltraHd,
                            _ => CameraResolution.FullHd
                        },
                        Fps = videoCap.Fps,
                        IsAutoFocus = _deviceService.Cam1.Focus.Focus != 0,
                        FocusValue = _deviceService.Cam1.Focus.Value,
                        BitRate = _configuration.GetValue<int>("RecordSetting:BitRate")
                    });
                if (!res.Success)
                {
                    _messageService.CreateMessage(
                        $"Cannot create settings for {_deviceService.Cam1.DisplayName} at bus {_deviceService.Cam1.Bus}",
                        MessageType.Error);
                }

                cameraRecordSetting =
                    _cameraSettingService.GetActiveCameraRecordSetting(_deviceService.Cam1.DisplayName,
                        _deviceService.Cam1.Bus);
            }


            var dataPath =
                Utilities.CreateRecordPath(SysSetting.RecordPath,
                    Regex.Replace(_deviceService.Cam1.DisplayName, "[^a-zA-Z0-9]", "").ToLower());
            var recordSetting = new RecordSetting
            {
                CameraRecordSetting = cameraRecordSetting,
                OnLine = online,
                GstVideoEncodeElement =
                    _configuration.GetValue<string>(
                        $"RecordSetting:GstVideoEncodeElement:{RuntimeInformation.RuntimeIdentifier}"),
                GstAudioEncodeElement = _configuration.GetValue<string>(
                    $"RecordSetting:GstAudioEncodeElement:{RuntimeInformation.RuntimeIdentifier}"),
                SplitAfterSeconds = _configuration.GetValue<int>("RecordSetting:MaxFileLengthSeconds"),
                Cam = _deviceService.Cam1,
                Cap = videoCap,
                Mic = _deviceService.Mic1,
                Path = dataPath,
                RtspPart = Path.Combine(uniqueId, SysSetting.StreamUrl1)
            };
            var recordPipeManager = RecordPipeManagers.FirstOrDefault(x => x.PipeName == "cam1");
            if (recordPipeManager != null)
            {
                recordPipeManager.RecordSetting = recordSetting;
            }
            else
            {
                recordPipeManager = new RecordPipeManager
                {
                    PipeName = "cam1",
                    RecordSetting = recordSetting
                };
                RecordPipeManagers.Add(recordPipeManager);
            }
        }

        if (_deviceService.Cam2 != null && _deviceService.Mic2 != null)
        {
            var videoCap = _deviceService.Cam2.Caps.FirstOrDefault();
            var cameraRecordSetting =
                _cameraSettingService.GetActiveCameraRecordSetting(_deviceService.Cam2.DisplayName,
                    _deviceService.Cam2.Bus);
            if (cameraRecordSetting == null)
            {
                var supportFps = _configuration.GetSection("RecordSetting:SupportedFps").Get<List<int>>();
                foreach (var fps in supportFps)
                {
                    videoCap = _deviceService.Cam2.Caps.FirstOrDefault(x => x.Fps == fps);
                    if (videoCap != null)
                    {
                        break;
                    }
                }

                var res = await _cameraSettingService.CreateOrUpdateCameraRecordSettingAsync(
                    new CreateOrUpdateCameraRecordSettingRequest
                    {
                        Name = _deviceService.Cam2.DisplayName,
                        Bus = _deviceService.Cam2.Bus,
                        IsActive = true,
                        Resolution = videoCap!.Width switch
                        {
                            1280 => CameraResolution.Hd,
                            1920 => CameraResolution.FullHd,
                            3840 => CameraResolution.UltraHd,
                            _ => CameraResolution.FullHd
                        },
                        Fps = videoCap.Fps,
                        IsAutoFocus = _deviceService.Cam2.Focus.Focus != 0,
                        FocusValue = _deviceService.Cam2.Focus.Value,
                        BitRate = _configuration.GetValue<int>("RecordSetting:BitRate")
                    });
                // create new camera record setting
                if (!res.Success)
                {
                    _messageService.CreateMessage(
                        $"Cannot create recording settings for {_deviceService.Cam2.DisplayName} at bus {_deviceService.Cam2.Bus}",
                        MessageType.Error);
                }

                cameraRecordSetting =
                    _cameraSettingService.GetActiveCameraRecordSetting(_deviceService.Cam2.DisplayName,
                        _deviceService.Cam2.Bus);
            }

            var dataPath =
                Utilities.CreateRecordPath(SysSetting.RecordPath,
                    Regex.Replace(_deviceService.Cam2.DisplayName, "[^a-zA-Z0-9]", "").ToLower());
            var recordSetting = new RecordSetting
            {
                CameraRecordSetting = cameraRecordSetting,
                OnLine = online,
                GstVideoEncodeElement =
                    _configuration.GetValue<string>(
                        $"RecordSetting:GstVideoEncodeElement:{RuntimeInformation.RuntimeIdentifier}"),
                GstAudioEncodeElement = _configuration.GetValue<string>(
                    $"RecordSetting:GstAudioEncodeElement:{RuntimeInformation.RuntimeIdentifier}"),
                SplitAfterSeconds = _configuration.GetValue<int>("RecordSetting:MaxFileLengthSeconds"),
                Cam = _deviceService.Cam2,
                Cap = videoCap,
                Mic = _deviceService.Mic2,
                Path = dataPath,
                RtspPart = Path.Combine(uniqueId, SysSetting.StreamUrl2)
            };
            var recordPipeManager = RecordPipeManagers.FirstOrDefault(x => x.PipeName == "cam2");
            if (recordPipeManager != null)
            {
                recordPipeManager.RecordSetting = recordSetting;
            }
            else
            {
                recordPipeManager = new RecordPipeManager
                {
                    PipeName = "cam2",
                    RecordSetting = recordSetting
                };
                RecordPipeManagers.Add(recordPipeManager);
            }
        }

        return true;
    }

    public void SetBitrate(CameraRecordSetting cameraRecordSetting)
    {
        var recordPipeManager = RecordPipeManagers.FirstOrDefault(x =>
            (x.RecordSetting.CameraRecordSetting.Name == cameraRecordSetting.Name) &&
            (x.RecordSetting.CameraRecordSetting.Bus == cameraRecordSetting.Bus));
        if (recordPipeManager == null)
        {
            _messageService.CreateMessage($"Device {cameraRecordSetting.Name} not found to set bitrate",
                MessageType.Error);
            return;
        }

        recordPipeManager.RecordPipe.UpdateBitrate(cameraRecordSetting.BitRate);
        recordPipeManager.RecordSetting.CameraRecordSetting = cameraRecordSetting;
    }

    public void UpdateNoiseProcessing(bool isNoiseProcessing, int? noiseProcessingLevel)
    {
        foreach (var recordPipeManager in RecordPipeManagers)
        {
            recordPipeManager.RecordPipe.UpdateNoiseProcessing(isNoiseProcessing, noiseProcessingLevel);
        }
    }

    public void UpdateEchoProcessing(bool isEchoProcessing, int? echoProcessingLevel)
    {
        foreach (var recordPipeManager in RecordPipeManagers)
        {
            recordPipeManager.RecordPipe.UpdateEchoProcessing(isEchoProcessing, echoProcessingLevel);
        }
    }
}