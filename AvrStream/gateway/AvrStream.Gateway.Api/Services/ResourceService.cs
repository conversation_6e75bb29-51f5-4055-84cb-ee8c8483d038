using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using AvrStream.Base.Models;

namespace AvrStream.Gateway.Api.Services;

public class ResourceService
{
    private readonly ILogger<ResourceService> _logger;

    public ResourceService(ILogger<ResourceService> logger)
    {
        _logger = logger;
    }

    public string GetDiskUsed(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk used")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=used {disk} | grep -v Used\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();
                _logger.LogDebug($"Output: {output}");
                var lines = output.Split("\n");
                _logger.LogDebug($"Output: {lines[0]}");
                return lines[0];
            }

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "0 G";
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public string GetDiskTotal(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk total")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=size {disk} | grep -v Size\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };
                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();
                var lines = output.Split("\n");
                _logger.LogDebug($"Output: {lines[0]}");
                return lines[0];
            }

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return "0 G";
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public bool CheckExceededCapacity(double percent, string disk)
    {
        var used = GetDiskUsed(disk);
        var total = GetDiskTotal(disk);
        _logger.LogDebug($"Disk: {disk}");
        _logger.LogDebug($"Used Value Raw: {used}");
        _logger.LogDebug($"Total Value Raw: {total}");
        if (!string.IsNullOrEmpty(used) && !string.IsNullOrEmpty(total))
        {
            var usedUnit = used[^1];
            var totalUnit = total[^1];
            var usedValueString = used.Substring(0, used.Length - 1).Replace(",", ".");
            var totalValueString = total.Substring(0, total.Length - 1).Replace(",", ".");
            double.TryParse(usedValueString, out var usedValue);
            double.TryParse(totalValueString, out var totalValue);
            if (usedUnit != totalUnit)
            {
                usedValue = NumberExtensions.ConvertToGb(usedUnit, usedValue);
                totalValue = NumberExtensions.ConvertToGb(totalUnit, totalValue);
            }

            _logger.LogDebug($"Used Value: {usedValue} GB");
            _logger.LogDebug($"Total Value: {totalValue} GB");
            if (totalValue > 0)
            {
                var percentUsed = usedValue / totalValue * 100;
                _logger.LogDebug($"Percent Used: {Math.Round(percentUsed, 3)}");
                if (percentUsed >= percent)
                {
                    return true;
                }
            }
        }

        return false;
    }

    public double GetMemory()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("memory")
                {
                    FileName = "/bin/bash",
                    Arguments = "-c \"free -m\"",
                    RedirectStandardOutput = true
                };

                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();

                var lines = output.Split("\n");
                var memory = lines[1].Split(" ", StringSplitOptions.RemoveEmptyEntries);
                return Math.Round(double.Parse(memory[2]) * 100 / double.Parse(memory[1]), 2);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
        }

        return 0;
    }

    public double GetCpu()
    {
        // // TODO: In Jetson Nano, command vmstat, top hold devices for recording
        // try
        // {
        //     if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //     {
        //         var info = new ProcessStartInfo("cpu")
        //         {
        //             FileName = "/bin/bash",
        //             Arguments = "-c \"vmstat 1 2|tail -1|awk '{print $15}'\"",
        //             RedirectStandardOutput = true
        //         };
        //         using var process = Process.Start(info);
        //         if (!process!.WaitForExit(1000))
        //         {
        //             process.Kill();
        //         }
        //
        //         var output = process!.StandardOutput.ReadToEnd();
        //         var lines = output.Split("\n");
        //         _logger.LogInformation($"CPU remaining: {lines[0]}");
        //         return 100 - Convert.ToDouble(lines[0]);
        //     }
        // }
        // catch (Exception e)
        // {
        //     _logger.LogError(e.Message);
        //     return 0;
        // }


        if (RecordService.IsStarting1 || RecordService.IsStarting2)
        {
            return -1;
        }
        try
        {
            //if (RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64"))
            //{
            //    var info = new ProcessStartInfo("tegrastats")
            //    {
            //        RedirectStandardOutput = true
            //    };
            //    // "RAM 2141/3964MB (lfb 156x4MB) SWAP 0/1982MB (cached 0MB) CPU [41%@1479,47%@1479,56%@1479,48%@1479] EMC_FREQ 0% GR3D_FREQ 0% PLL@34C CPU@36.5C PMIC@50C GPU@33.5C AO@45"
            //    using var process = Process.Start(info);
            //    if (!process!.WaitForExit(2000))
            //    {
            //        process.Kill();
            //    }

            //    var pattern = @"CPU \[(\d+)%@(\d+),(\d+)%@(\d+),(\d+)%@(\d+),(\d+)%@(\d+)\]";
            //    var output = process!.StandardOutput.ReadToEnd();
            //    _logger.LogDebug($"Raw CPU Usage: {output}");
            //    var lines = output.Split("\n");
            //    var information = lines[0].Trim().Replace("\r", "");
            //    _logger.LogDebug($"CPU Usage: {information}");
            //    var match = Regex.Match(information, pattern);

            //    if (match.Success)
            //    {
            //        // Extract CPU usage values
            //        _logger.LogDebug(
            //            $"CPU Usage Match: {match.Groups[1].Value}, {match.Groups[3].Value}, {match.Groups[5].Value}, {match.Groups[7].Value}");
            //        var cpu1 = int.Parse(match.Groups[1].Value);
            //        var cpu2 = int.Parse(match.Groups[3].Value);
            //        var cpu3 = int.Parse(match.Groups[5].Value);
            //        var cpu4 = int.Parse(match.Groups[7].Value);

            //        // Calculate total CPU usage (average of all CPU cores)
            //        var totalCpuUsage = (cpu1 + cpu2 + cpu3 + cpu4) / 4.0;
            //        return totalCpuUsage;
            //    }

            //    return 0;
            //}

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("cpu")
                {
                    FileName = "/bin/bash",
                    Arguments =
                        "-c \"top -bn2 | grep '%Cpu' | tail -1 | grep -P '(....|...) id,'|awk '{print 100-$8}'\"",
                    RedirectStandardOutput = true
                };

                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();
                var lines = output.Split("\n");

                return Convert.ToDouble(lines[0]);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return -1;
        }

        return 0;
    }

    public long GetDiskAvailableKb(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -k --output=avail {disk} | grep -v Avail\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetDiskTotalBytes(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -B1 --output=size {disk} | grep -v 1B-blocks\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            //_logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetDiskAvailableBytes(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -B1 --output=avail {disk} | grep -v Avail\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            //_logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetRamTotal()
    {
        try
        {
            var info = new ProcessStartInfo("ram total")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"free -m| grep Mem | awk '{print $2}'\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get Total Ram Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    // get cpu temperature via (cat /sys/class/thermal/thermal_zone1/temp)
    public int GetCpuTemperature()
    {
        try
        {
            var info = new ProcessStartInfo("cpu temperature")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"cat /sys/class/thermal/thermal_zone1/temp\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get CPU Temperature Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (int.TryParse(valueString, out var value))
                return value / 1000;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public int GetGpuTemperature()
    {
        try
        {
            var info = new ProcessStartInfo("gpu temperature")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"cat /sys/class/thermal/thermal_zone2/temp\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get GPU Temperature Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (int.TryParse(valueString, out int value))
                return value / 1000;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public (long total, long available) GetStorageInfo(string dataPath)
    {
        // NOTE: only support removable devices on linux right now^M
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            try
            {
                var info = new ProcessStartInfo("df", "-k")
                {
                    RedirectStandardOutput = true
                };

                var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process.StandardOutput.ReadToEnd();
                var blocks = output.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries)
                    .Skip(1) /* ignore header column */
                    .Select(t =>
                    {
                        var strs = t.Split(new[] { " " },
                            StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);

                        if (strs.Length == 6)
                        {
                            // 1K-blocks in column 1
                            var size = long.Parse(strs[1]) * 1024;
                            var used = long.Parse(strs[2]) * 1024;
                            var available = long.Parse(strs[3]) * 1024;
                            var mount = strs[5];

                            return new
                            {
                                mount,
                                size,
                                used,
                                available
                            };
                        }

                        return null;
                    })
                    .Where(t => t != null)
                    .Select(t => t!)
                    .ToList();

                _logger.LogDebug($"Blocks size info: {JsonSerializer.Serialize(blocks)}");

                var device = blocks.FirstOrDefault(t => dataPath.StartsWith(t.mount)) ??
                             blocks.FirstOrDefault(t => t.mount == "/");

                return (device?.size ?? long.MaxValue, device?.available ?? long.MaxValue);
            }
            catch (Exception e)
            {
                _logger.LogError(e, nameof(GetStorageInfo));
            }
        }

        return (long.MaxValue, long.MaxValue);
    }
}