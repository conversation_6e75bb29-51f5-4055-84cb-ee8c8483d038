using System.Diagnostics;
using AvrStream.Base.Streamer.Devices;
using AvrStream.Base.Streamer.Pipes;
using Gst;
using Microsoft.Extensions.Logging;
using MessageType = Gst.MessageType;

namespace AvrStream.Gateway.Streamer.Pipes;
// gst-launch-1.0  v4l2src device=/dev/video2 io-mode=4 name=vid_src do-timestamp=True ! image/jpeg, width=1280, height=720, framerate=30/1 ! jpegdec ! videoflip video-direction=0 ! clockoverlay halignment=2 valignment=2 font-desc="Verdana Medium 9" time-format="%d/%m/%Y %H:%M:%S" ! videoconvert ! nvh264enc ! h264parse config-interval=-1 ! s_local. alsasrc device=hw:2 name=mic_src do-timestamp=True ! audioconvert ! audioresample ! opusenc ! opusparse ! s_local. rtspclientsink name=s_local location=rtsp://localhost:8554/888b3675cbf34aa8a940578c72511140/stream1

public class PreviewPipe : BasePipe
{
    private static readonly Func<AvrCam, AvrMic, string, string, string, string> PreviewFormat =
        (cam, mic, camEncode, micEncode, part) =>
            @$"{cam.GetGstInput()} ! {cam.Caps.First(t => t.IsNative()).GetGstCapInput()} ! {camEncode} ! h264parse config-interval=-1 ! s_local. {mic.GetGstInput()} ! audioconvert ! audioresample ! {micEncode} ! s_local. rtspclientsink name=s_local location=rtsp://localhost:8554/{part}
";

    private bool _eosRaise;

    public PreviewPipe(ILoggerFactory loggerFactory, AvrCam cam, AvrMic mic, string camEncode, string micEncode,
        string part, Action onError) :
        base(loggerFactory)
    {
        var desc = PreviewFormat(cam, mic, camEncode, micEncode, part);
        Logger.LogInformation($"PREVIEW: gst-launch-1.0 {desc}");
        Instance = (Pipeline)Parse.Launch(desc);

        Instance.Bus.AddWatch((_, message) =>
        {
            switch (message.Type)
            {
                case MessageType.Error:
                    message.ParseError(out var err, out var debug);
                    Logger.LogError($"Error from: {message.Src.Name}: {err.Message}; Debug info: {debug}");
                    onError?.Invoke();
                    Dispose();
                    break;
                case MessageType.Eos:
                    Logger.LogInformation("Got EOS, disposing...");
                    _eosRaise = true;
                    Dispose();
                    break;
            }

            return true;
        });

        var ret = Instance.SetState(State.Playing);
        if (ret == StateChangeReturn.Failure)
            throw new Exception("Could not start preview as hardware failed");
    }

    public void Stop()
    {
        Logger.LogInformation("Stopping preview pipeline by send EOS...");
        var st = Stopwatch.StartNew();
        Instance.SendEvent(Event.NewEos());
        while (!_eosRaise)
        {
            Thread.Sleep(100);
        }
        Logger.LogInformation($"Preview pipeline stopped in {st.ElapsedMilliseconds}ms");
        st.Stop();
    }
}