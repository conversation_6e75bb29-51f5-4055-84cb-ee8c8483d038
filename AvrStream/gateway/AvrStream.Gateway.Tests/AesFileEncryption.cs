using System.Diagnostics;
using System.Security.Cryptography;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace AvrStream.Gateway.Tests;

[TestClass]
public class AesFileEncryption
{
    private async Task EncryptMp4FileAsync(string inputFilePath, string outputFilePath, byte[] key, byte[] iv)
    {
        if (key.Length != 32) // 256-bit key
            throw new ArgumentException("Key size must be 256 bits (32 bytes).");
        if (iv.Length != 16) // AES block size for CBC mode
            throw new ArgumentException("IV size must be 16 bytes.");

        await using FileStream inputFileStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read);
        await using FileStream outputFileStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);
        using Aes aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        // Create an encryptor to perform the stream transform.
        await using CryptoStream cryptoStream =
            new CryptoStream(outputFileStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
        await inputFileStream.CopyToAsync(cryptoStream);
    }

    private async Task DecryptMp4FileAsync(string inputFilePath, string outputFilePath, byte[] key, byte[] iv)
    {
        if (key.Length != 32) // 256-bit key
            throw new ArgumentException("Key size must be 256 bits (32 bytes).");
        if (iv.Length != 16) // AES block size for CBC mode
            throw new ArgumentException("IV size must be 16 bytes.");

        try
        {
            await using FileStream inputFileStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read);
            await using FileStream outputFileStream =
                new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);
            using Aes aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            // Create a decryptor to perform the stream transform.
            await using CryptoStream cryptoStream =
                new CryptoStream(inputFileStream, aes.CreateDecryptor(), CryptoStreamMode.Read);
            await cryptoStream.CopyToAsync(outputFileStream);
        }
        catch (IOException ioEx)
        {
            Console.WriteLine($"File operation error: {ioEx.Message}");
            throw;
        }
        catch (CryptographicException cryptoEx)
        {
            Console.WriteLine($"Decryption error: {cryptoEx.Message}");
            throw;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An unexpected error occurred: {ex.Message}");
            throw;
        }
    }


    private byte[] GenerateRandomKey(int size)
    {
        byte[] key = new byte[size];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(key);
        }

        return key;
    }

    [TestMethod]
    public void EncryptTest()
    {
        // string inputFilePath = "/home/<USER>/RiderProjects/avr-stream/AvrStream/AvrStream.Gateway.Tests/input.mp4";
        // string encryptFilePath =
        //     "/home/<USER>/RiderProjects/avr-stream/AvrStream/AvrStream.Gateway.Tests/encrypted_output.mp4";
        // string decryptFilePath =
        //     "/home/<USER>/RiderProjects/avr-stream/AvrStream/AvrStream.Gateway.Tests/decrypt_output.mp4";

        AesFileEncryption fileEncryption = new AesFileEncryption();

        // Generate or retrieve a 256-bit AES key and 128-bit IV
        byte[] key = fileEncryption.GenerateRandomKey(32); // 32 bytes for AES-256
        byte[] iv = fileEncryption.GenerateRandomKey(16); // 16 bytes for the AES block size


        string keyString = BitConverter.ToString(key).Replace("-", "");

        string ivString = BitConverter.ToString(iv).Replace("-", "");

        byte[] byteArrayKey = Enumerable.Range(0, keyString.Length / 2)
            .Select(i => Convert.ToByte(keyString.Substring(i * 2, 2), 16))
            .ToArray();

        byte[] byteArrayIv = Enumerable.Range(0, ivString.Length / 2)
            .Select(i => Convert.ToByte(ivString.Substring(i * 2, 2), 16))
            .ToArray();
        string keyString2 = BitConverter.ToString(byteArrayKey).Replace("-", "");
        string ivString2 = BitConverter.ToString(byteArrayIv).Replace("-", "");

        Assert.AreEqual(keyString, keyString2);
        Assert.AreEqual(ivString, ivString2);

        var stopwatch = Stopwatch.StartNew();
        // await fileEncryption.EncryptMp4FileAsync(inputFilePath, encryptFilePath, key, iv);
        // Console.WriteLine($"Encrypt file with time: {stopwatch.ElapsedMilliseconds} ms");
        //
        // stopwatch.Restart();
        // await fileEncryption.DecryptMp4FileAsync(encryptFilePath, decryptFilePath, key, iv);
        Console.WriteLine($"Decrypt file with time: {stopwatch.ElapsedMilliseconds} ms");
        Console.WriteLine("MP4 file encrypted successfully.");
    }
}